basePath: /api/
definitions:
  gorm.DeletedAt:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
  http.HTTPError:
    properties:
      code:
        example: 400
        type: integer
      message:
        example: status bad request
        type: string
    type: object
  models.AddQuestionsToTestRequest:
    properties:
      question_ids:
        items:
          type: integer
        type: array
      section_name:
        type: string
    required:
    - question_ids
    - section_name
    type: object
  models.AdminForCreate:
    properties:
      contact_address:
        type: string
      email:
        type: string
      full_name:
        type: string
      password:
        minLength: 6
        type: string
      phone_number:
        type: string
    required:
    - email
    - full_name
    - password
    - phone_number
    type: object
  models.Chapter:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      studyMaterials:
        items:
          $ref: '#/definitions/models.StudyMaterial'
        type: array
      subject:
        $ref: '#/definitions/models.Subject'
      subjectID:
        type: integer
      updatedAt:
        type: string
      videos:
        items:
          $ref: '#/definitions/models.Video'
        type: array
    type: object
  models.ChapterDetails:
    properties:
      display_name:
        type: string
      id:
        type: integer
      name:
        type: string
      study_materials:
        items:
          $ref: '#/definitions/models.StudyMaterialForGet'
        type: array
      videos:
        items:
          $ref: '#/definitions/models.VideoForGet'
        type: array
    type: object
  models.ChapterForCreate:
    properties:
      displayName:
        type: string
      name:
        type: string
      subjectName:
        type: string
    type: object
  models.Comment:
    properties:
      commentText:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      material:
        $ref: '#/definitions/models.StudyMaterial'
      materialID:
        description: 'Optional: or to a study material'
        type: integer
      responses:
        description: Replies to this comment
        items:
          $ref: '#/definitions/models.Response'
        type: array
      updatedAt:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/models.User'
        description: Assumes a User model exists
      userID:
        description: FK to users
        type: integer
      video:
        $ref: '#/definitions/models.Video'
      videoID:
        description: 'Optional: comments can belong to a video'
        type: integer
    type: object
  models.CommentForCreate:
    properties:
      comment_text:
        type: string
      material_id:
        type: integer
      video_id:
        type: integer
    required:
    - comment_text
    type: object
  models.CommentWithResponses:
    properties:
      comment_text:
        type: string
      created_at:
        type: string
      id:
        type: integer
      material_id:
        type: integer
      responses:
        items:
          $ref: '#/definitions/models.ResponseOutput'
        type: array
      user_id:
        type: integer
      user_name:
        type: string
      video_id:
        type: integer
    type: object
  models.CommentsResponse:
    properties:
      comments:
        items:
          $ref: '#/definitions/models.CommentWithResponses'
        type: array
      material_id:
        type: integer
      video_id:
        type: integer
    type: object
  models.Content:
    properties:
      pdfs:
        items:
          $ref: '#/definitions/models.StudyMaterialForGet'
        type: array
      videos:
        items:
          $ref: '#/definitions/models.VideoForGet'
        type: array
    type: object
  models.Course:
    properties:
      courseType:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      id:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      subjects:
        items:
          $ref: '#/definitions/models.Subject'
        type: array
      tests:
        items:
          $ref: '#/definitions/models.Test'
        type: array
      updatedAt:
        type: string
    type: object
  models.CourseDetails:
    properties:
      course_type:
        type: string
      created_at:
        type: string
      description:
        type: string
      discount:
        type: number
      duration_in_days:
        type: integer
      id:
        type: integer
      is_free:
        type: boolean
      name:
        type: string
      price:
        type: integer
      subjects:
        items:
          $ref: '#/definitions/models.SubjectDetails'
        type: array
      updated_at:
        type: string
    type: object
  models.CourseForCreate:
    properties:
      courseType:
        type: string
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      subjectNames:
        items:
          type: string
        type: array
    type: object
  models.CourseWithPurchased:
    properties:
      courseType:
        type: string
      description:
        type: string
      discount:
        type: number
      durationInDays:
        type: integer
      isFree:
        type: boolean
      name:
        type: string
      price:
        type: integer
      purchased:
        type: boolean
    type: object
  models.CoursesByCategory:
    properties:
      free_courses:
        items:
          $ref: '#/definitions/models.CoursesByType'
        type: array
      paid_courses:
        items:
          $ref: '#/definitions/models.CoursesByType'
        type: array
    type: object
  models.CoursesByType:
    properties:
      course_type:
        type: string
      courses:
        items:
          $ref: '#/definitions/models.CourseWithPurchased'
        type: array
    type: object
  models.CreatedAdminResponse:
    properties:
      admin:
        $ref: '#/definitions/models.SimpleEntityResponse'
      token:
        type: string
    type: object
  models.CreatedStudentResponse:
    properties:
      student:
        $ref: '#/definitions/models.SimpleEntityResponse'
      token:
        type: string
    type: object
  models.Credentials:
    properties:
      password:
        type: string
      user_email:
        type: string
    type: object
  models.Difficulty:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      updatedAt:
        type: string
    type: object
  models.EvaluationStatus:
    enum:
    - NotApplicable
    - Pending
    - Evaluated
    type: string
    x-enum-comments:
      EvaluationStatusEvaluated: All student responses are evaluated
      EvaluationStatusNotApplicable: Test not yet attempted by any student
      EvaluationStatusPending: Some students have responses but not evaluated
    x-enum-varnames:
    - EvaluationStatusNotApplicable
    - EvaluationStatusPending
    - EvaluationStatusEvaluated
  models.FormulaCard:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      imageUrl:
        type: string
      name:
        type: string
      topic:
        $ref: '#/definitions/models.Topic'
      topicID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.FormulaCardForCreate:
    properties:
      image_url:
        type: string
      name:
        type: string
    required:
    - image_url
    - name
    type: object
  models.FormulaCardSummary:
    properties:
      chapter_name:
        type: string
      id:
        type: integer
      image_url:
        type: string
      name:
        type: string
      subject_name:
        type: string
      topic_name:
        type: string
    type: object
  models.FormulaCardsByChapter:
    properties:
      chapter_name:
        type: string
      count:
        type: integer
      topics:
        items:
          $ref: '#/definitions/models.FormulaCardsByTopic'
        type: array
    type: object
  models.FormulaCardsBySubject:
    properties:
      chapters:
        items:
          $ref: '#/definitions/models.FormulaCardsByChapter'
        type: array
      count:
        type: integer
      subject_name:
        type: string
    type: object
  models.FormulaCardsByTopic:
    properties:
      count:
        type: integer
      formula_cards:
        items:
          $ref: '#/definitions/models.FormulaCardSummary'
        type: array
      topic_name:
        type: string
    type: object
  models.FormulaCardsForCreate:
    properties:
      chapter_name:
        type: string
      formula_cards:
        items:
          $ref: '#/definitions/models.FormulaCardForCreate'
        minItems: 1
        type: array
      subject_name:
        type: string
      topic_name:
        type: string
    required:
    - chapter_name
    - formula_cards
    - subject_name
    - topic_name
    type: object
  models.Institution:
    properties:
      city_or_town:
        type: string
      contact_name:
        type: string
      contact_number:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      state:
        type: string
      updatedAt:
        type: string
    type: object
  models.InstitutionForCreate:
    properties:
      city_or_town:
        type: string
      contact_name:
        type: string
      contact_number:
        type: string
      name:
        type: string
      state:
        type: string
    required:
    - city_or_town
    - contact_name
    - contact_number
    - name
    - state
    type: object
  models.InstitutionForUpdate:
    properties:
      city_or_town:
        type: string
      contact_name:
        type: string
      contact_number:
        type: string
      name:
        type: string
      state:
        type: string
    required:
    - city_or_town
    - contact_name
    - contact_number
    - name
    - state
    type: object
  models.MaterialForCreate:
    properties:
      chapterName:
        type: string
      displayName:
        type: string
      name:
        type: string
      url:
        type: string
    type: object
  models.Option:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      isCorrect:
        description: Correctness flag
        type: boolean
      optionImageURL:
        description: Optional image URL
        type: string
      optionText:
        description: Option content
        type: string
      question:
        allOf:
        - $ref: '#/definitions/models.Question'
        description: GORM association
      questionID:
        description: Foreign key to Questions
        type: integer
      updatedAt:
        type: string
    type: object
  models.OptionForCreate:
    properties:
      is_correct:
        type: boolean
      option_image_url:
        type: string
      option_text:
        type: string
    required:
    - option_text
    type: object
  models.PreviousYearPaper:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      examType:
        type: string
      id:
        type: integer
      month:
        type: integer
      pdfUrl:
        type: string
      updatedAt:
        type: string
      year:
        type: integer
    type: object
  models.PreviousYearPaperForCreate:
    properties:
      exam_type:
        enum:
        - IIT-JEE
        - NEET
        type: string
      month:
        maximum: 12
        minimum: 1
        type: integer
      pdf_url:
        type: string
      year:
        maximum: 2100
        minimum: 1900
        type: integer
    required:
    - exam_type
    - month
    - pdf_url
    - year
    type: object
  models.PreviousYearPapersByExamType:
    properties:
      exam_type:
        type: string
      papers:
        items:
          $ref: '#/definitions/models.PreviousYearPaper'
        type: array
    type: object
  models.PreviousYearPapersForCreate:
    properties:
      papers:
        items:
          $ref: '#/definitions/models.PreviousYearPaperForCreate'
        minItems: 1
        type: array
    required:
    - papers
    type: object
  models.Question:
    properties:
      correctAnswer:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      difficulty:
        $ref: '#/definitions/models.Difficulty'
      difficultyID:
        type: integer
      fileUrl:
        type: string
      id:
        type: integer
      imageUrl:
        type: string
      options:
        description: One-to-many relationship with options
        items:
          $ref: '#/definitions/models.Option'
        type: array
      questionType:
        type: string
      text:
        type: string
      topic:
        $ref: '#/definitions/models.Topic'
      topicID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.QuestionForCreate:
    properties:
      correct_answer:
        type: string
      difficulty_name:
        type: string
      file_url:
        type: string
      image_url:
        type: string
      options:
        items:
          $ref: '#/definitions/models.OptionForCreate'
        type: array
      question_type:
        type: string
      text:
        type: string
      topic_name:
        type: string
    required:
    - difficulty_name
    - question_type
    - text
    - topic_name
    type: object
  models.RemoveQuestionsFromTestRequest:
    properties:
      question_ids:
        items:
          type: integer
        type: array
      section_name:
        type: string
    required:
    - question_ids
    - section_name
    type: object
  models.Response:
    properties:
      comment:
        $ref: '#/definitions/models.Comment'
      commentID:
        description: FK to Comment
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      responseText:
        type: string
      updatedAt:
        type: string
      user:
        $ref: '#/definitions/models.User'
      userID:
        description: FK to User
        type: integer
    type: object
  models.ResponseForCreate:
    properties:
      comment_id:
        type: integer
      response_text:
        type: string
    required:
    - comment_id
    - response_text
    type: object
  models.ResponseOutput:
    properties:
      created_at:
        type: string
      id:
        type: integer
      response_text:
        type: string
      user_id:
        type: integer
      user_name:
        type: string
    type: object
  models.Section:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      sectionType:
        $ref: '#/definitions/models.SectionType'
      sectionTypeID:
        type: integer
      test:
        $ref: '#/definitions/models.Test'
      testID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SectionType:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      name:
        type: string
      negativeMarks:
        type: number
      positiveMarks:
        type: number
      questionCount:
        type: integer
      subject:
        $ref: '#/definitions/models.Subject'
      subjectID:
        type: integer
      updatedAt:
        type: string
    type: object
  models.SectionTypeForCreate:
    properties:
      name:
        type: string
      negativeMarks:
        type: number
      positiveMarks:
        type: number
      questionCount:
        type: integer
      subjectName:
        type: string
    type: object
  models.SendVerificationCodeRequest:
    properties:
      phone_number:
        type: string
    required:
    - phone_number
    type: object
  models.SendVerificationCodeResponse:
    properties:
      code_sent:
        type: boolean
      expires_at:
        type: string
      message:
        type: string
    type: object
  models.SimpleEntityResponse:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  models.Student:
    properties:
      city_or_town:
        type: string
      class:
        type: string
      courses:
        items:
          $ref: '#/definitions/models.Course'
        type: array
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      institute:
        type: string
      parent_email:
        type: string
      parent_phone:
        type: string
      state:
        type: string
      stream:
        type: string
      updatedAt:
        type: string
      user:
        $ref: '#/definitions/models.User'
      userID:
        type: integer
    type: object
  models.StudentEvaluationResult:
    properties:
      correct_answers:
        type: integer
      evaluation_time:
        type: string
      message:
        type: string
      student_id:
        type: integer
      student_name:
        type: string
      total_questions:
        type: integer
      total_score:
        type: integer
    type: object
  models.StudentForCreate:
    properties:
      city_or_town:
        type: string
      class:
        enum:
        - 9th
        - 10th
        - 11th
        - 12th
        - dropper
        type: string
      contactAddress:
        type: string
      email:
        type: string
      fullName:
        type: string
      institute:
        type: string
      parent_email:
        type: string
      parent_phone:
        type: string
      phoneNumber:
        type: string
      state:
        type: string
      stream:
        enum:
        - IIT-JEE
        - NEET
        type: string
    type: object
  models.StudentForList:
    properties:
      city_or_town:
        type: string
      class:
        type: string
      created_at:
        type: string
      email:
        type: string
      full_name:
        type: string
      id:
        type: integer
      institute:
        type: string
      parent_email:
        type: string
      parent_phone:
        type: string
      phone_number:
        type: string
      state:
        type: string
      stream:
        type: string
    type: object
  models.StudentRankingInfo:
    properties:
      final_marks:
        type: integer
      percentile:
        type: number
      rank:
        type: integer
      student_email:
        type: string
      student_id:
        type: integer
      student_name:
        type: string
      total_negative_marks:
        type: integer
      total_positive_marks:
        type: integer
    type: object
  models.StudentTestResponsesResult:
    properties:
      message:
        type: string
      responses:
        items:
          $ref: '#/definitions/models.TestResponse'
        type: array
      responses_recorded_at:
        type: string
      student_id:
        type: integer
      student_name:
        type: string
      test_id:
        type: integer
      test_name:
        type: string
      total_score:
        type: integer
    type: object
  models.StudyMaterial:
    properties:
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
      url:
        type: string
    type: object
  models.StudyMaterialForGet:
    properties:
      chapter_id:
        type: integer
      display_name:
        type: string
      name:
        type: string
      updated_at:
        type: string
      url:
        type: string
    type: object
  models.Subject:
    properties:
      chapters:
        items:
          $ref: '#/definitions/models.Chapter'
        type: array
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
    type: object
  models.SubjectDetails:
    properties:
      chapters:
        items:
          $ref: '#/definitions/models.ChapterDetails'
        type: array
      display_name:
        type: string
      id:
        type: integer
      name:
        type: string
    type: object
  models.SubjectForCreate:
    properties:
      displayName:
        type: string
      name:
        type: string
    type: object
  models.Test:
    properties:
      active:
        type: boolean
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      description:
        type: string
      evaluationStatus:
        $ref: '#/definitions/models.EvaluationStatus'
      fromTime:
        type: string
      id:
        type: integer
      name:
        type: string
      sections:
        items:
          $ref: '#/definitions/models.Section'
        type: array
      testType:
        $ref: '#/definitions/models.TestType'
      testTypeID:
        type: integer
      toTime:
        type: string
      updatedAt:
        type: string
    type: object
  models.TestEvaluationRequest:
    properties:
      test_id:
        type: integer
    required:
    - test_id
    type: object
  models.TestEvaluationResult:
    properties:
      message:
        type: string
      student_results:
        items:
          $ref: '#/definitions/models.StudentEvaluationResult'
        type: array
      test_id:
        type: integer
      test_name:
        type: string
      total_students_evaluated:
        type: integer
    type: object
  models.TestForCreate:
    properties:
      description:
        type: string
      fromTime:
        type: string
      name:
        type: string
      testTypeName:
        type: string
      toTime:
        type: string
    type: object
  models.TestForGet:
    properties:
      active:
        type: boolean
      description:
        type: string
      evaluationStatus:
        $ref: '#/definitions/models.EvaluationStatus'
      fromTime:
        type: string
      id:
        type: integer
      name:
        type: string
      sectionNames:
        items:
          type: string
        type: array
      testType:
        type: string
      toTime:
        type: string
    type: object
  models.TestQuestionInfo:
    properties:
      difficulty_name:
        type: string
      file_url:
        type: string
      id:
        type: integer
      image_url:
        type: string
      options:
        items:
          $ref: '#/definitions/models.Option'
        type: array
      question_type:
        type: string
      subject_name:
        type: string
      text:
        type: string
      topic_name:
        type: string
    type: object
  models.TestQuestionsResult:
    properties:
      message:
        type: string
      sections:
        items:
          $ref: '#/definitions/models.TestSectionInfo'
        type: array
      test_id:
        type: integer
      test_name:
        type: string
      test_type:
        type: string
    type: object
  models.TestRankingResult:
    properties:
      average_marks:
        type: number
      highest_marks:
        type: integer
      lowest_marks:
        type: integer
      message:
        type: string
      student_rankings:
        items:
          $ref: '#/definitions/models.StudentRankingInfo'
        type: array
      test_id:
        type: integer
      test_name:
        type: string
      total_students:
        type: integer
    type: object
  models.TestResponse:
    properties:
      calculatedScore:
        description: Nullable, can be set after evaluation
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      isCorrect:
        description: Automatically evaluated
        type: boolean
      question:
        $ref: '#/definitions/models.Question'
      questionID:
        description: FK to questions
        type: integer
      responseText:
        description: Nullable for text answers
        type: string
      selectedOptionIDs:
        description: PostgreSQL array type (use pgx or pq)
        items:
          type: integer
        type: array
      student:
        allOf:
        - $ref: '#/definitions/models.Student'
        description: Assumes you have a Student model
      studentID:
        description: FK to students
        type: integer
      test:
        $ref: '#/definitions/models.Test'
      testID:
        description: FK to tests
        type: integer
      updatedAt:
        type: string
    type: object
  models.TestResponseForCreate:
    properties:
      question_id:
        type: integer
      response_text:
        type: string
      selected_option_ids:
        items:
          type: integer
        type: array
    required:
    - question_id
    type: object
  models.TestResponseResult:
    properties:
      calculated_score:
        type: integer
      is_correct:
        type: boolean
      message:
        type: string
      question_id:
        type: integer
    type: object
  models.TestResponsesForCreate:
    properties:
      responses:
        items:
          $ref: '#/definitions/models.TestResponseForCreate'
        minItems: 1
        type: array
      test_id:
        type: integer
    required:
    - responses
    - test_id
    type: object
  models.TestResponsesResult:
    properties:
      correct_answers:
        type: integer
      message:
        type: string
      response_results:
        items:
          $ref: '#/definitions/models.TestResponseResult'
        type: array
      student_id:
        type: integer
      test_id:
        type: integer
      total_questions:
        type: integer
      total_score:
        type: integer
    type: object
  models.TestSectionInfo:
    properties:
      display_name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.TestQuestionInfo'
        type: array
      section_id:
        type: integer
      section_name:
        type: string
    type: object
  models.TestType:
    properties:
      createdAt:
        type: string
      id:
        type: integer
      name:
        type: string
      sectionTypes:
        items:
          $ref: '#/definitions/models.SectionType'
        type: array
      updatedAt:
        type: string
    type: object
  models.TestTypeForCreate:
    properties:
      name:
        type: string
      sectionTypeNames:
        items:
          type: string
        type: array
    type: object
  models.Topic:
    properties:
      chapter:
        $ref: '#/definitions/models.Chapter'
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      formulaCards:
        items:
          $ref: '#/definitions/models.FormulaCard'
        type: array
      id:
        type: integer
      name:
        type: string
      questions:
        items:
          $ref: '#/definitions/models.Question'
        type: array
      updatedAt:
        type: string
    type: object
  models.TopicForCreate:
    properties:
      chapterName:
        type: string
      name:
        type: string
    type: object
  models.Transaction:
    properties:
      amount:
        description: Amount in smallest currency unit (e.g., paise)
        type: integer
      courses:
        items:
          $ref: '#/definitions/models.Course'
        type: array
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      id:
        type: integer
      paymentMethod:
        description: e.g., "UPI", "CARD", "NET_BANKING"
        type: string
      paymentReference:
        description: External payment gateway reference
        type: string
      status:
        type: string
      student:
        $ref: '#/definitions/models.Student'
      studentID:
        type: integer
      transactionDate:
        type: string
      updatedAt:
        type: string
    type: object
  models.TransactionForCreate:
    properties:
      course_ids:
        items:
          type: integer
        type: array
      payment_method:
        type: string
    required:
    - course_ids
    - payment_method
    type: object
  models.TransactionHistory:
    properties:
      total_amount:
        type: integer
      transactions:
        items:
          $ref: '#/definitions/models.TransactionSummary'
        type: array
    type: object
  models.TransactionStatusUpdate:
    properties:
      payment_reference:
        type: string
      status:
        enum:
        - PENDING
        - COMPLETED
        - FAILED
        - CANCELLED
        type: string
    required:
    - status
    type: object
  models.TransactionSummary:
    properties:
      amount:
        type: integer
      courses:
        items:
          $ref: '#/definitions/models.CourseWithPurchased'
        type: array
      id:
        type: integer
      payment_method:
        type: string
      payment_reference:
        type: string
      status:
        type: string
      transaction_date:
        type: string
    type: object
  models.UpdatePassword:
    properties:
      new_password:
        type: string
    type: object
  models.User:
    properties:
      contactAddress:
        type: string
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      email:
        type: string
      emailVerified:
        type: boolean
      fullName:
        type: string
      id:
        type: integer
      passwordHash:
        type: string
      phoneNumber:
        type: string
      phoneVerified:
        type: boolean
      role:
        type: string
      updatedAt:
        type: string
    type: object
  models.VerifyCodeRequest:
    properties:
      code:
        type: string
      phone_number:
        type: string
    required:
    - code
    - phone_number
    type: object
  models.VerifyCodeResponse:
    properties:
      message:
        type: string
      status:
        type: string
      verified:
        type: boolean
    type: object
  models.Video:
    properties:
      chapterID:
        type: integer
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      displayName:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
      videoUrl:
        type: string
      viewCount:
        type: integer
    type: object
  models.VideoForCreate:
    properties:
      chapterName:
        type: string
      displayName:
        type: string
      name:
        type: string
      videoUrl:
        type: string
    type: object
  models.VideoForGet:
    properties:
      chapter_id:
        type: integer
      display_name:
        type: string
      name:
        type: string
      updated_at:
        type: string
      video_url:
        type: string
      view_count:
        type: integer
    type: object
host: ************:443
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Backend server for ZIA Academy.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: ZIA Academy App
  version: "1.0"
paths:
  /admins:
    post:
      consumes:
      - application/json
      description: |-
        Create a new admin user with role "Admin"

        Field Constraints:
        - fullName: Required field
        - email: Must be valid email format and unique across all users (required)
        - phoneNumber: Must be unique across all users (required)
        - password: Must be at least 6 characters long (required)
        - contactAddress: Optional field
      parameters:
      - description: Admin user details
        in: body
        name: admin
        required: true
        schema:
          $ref: '#/definitions/models.AdminForCreate'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.CreatedAdminResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Create Admin User
      tags:
      - admins
  /chapters:
    get:
      consumes:
      - application/json
      description: get chapters for a subject_id
      parameters:
      - description: Subject ID
        in: query
        name: subject_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Chapter'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Chapters
      tags:
      - chapters
    post:
      consumes:
      - application/json
      description: |-
        create new chapter

        Field Constraints:
        - name: Chapter name must be unique (required)
        - displayName: Display name for the chapter (required)
        - subjectName: Must reference an existing subject (required)
      parameters:
      - description: chapter details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.ChapterForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateChapters
      tags:
      - chapters
  /comments:
    get:
      consumes:
      - application/json
      description: Get comments for a video or study material. For students, returns
        only their comments. For admins, returns all comments.
      parameters:
      - description: Video ID
        in: query
        name: video_id
        type: integer
      - description: Study Material ID
        in: query
        name: material_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommentsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Comments
      tags:
      - comments
    post:
      consumes:
      - application/json
      description: Add a comment to a video or study material
      parameters:
      - description: comment details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.CommentForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Comment'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Add Comment
      tags:
      - comments
  /content:
    get:
      consumes:
      - application/json
      description: get videos and study materials as flat lists sorted by updated
        date
      parameters:
      - description: Subject Name (optional - if not provided, returns content for
          all subjects)
        in: query
        name: subject_name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Content'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Content
      tags:
      - content
  /courses:
    get:
      consumes:
      - application/json
      description: get courses for the logged in user. Returns all courses for non-student
        users (admin, etc.) and filtered courses for students (free courses + enrolled
        paid courses)
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CoursesByCategory'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Courses
      tags:
      - courses
    post:
      consumes:
      - application/json
      description: |-
        create new course

        Field Constraints:
        - courseType: Must be one of 'IIT-JEE', 'NEET' (required)
        - name: Must be unique across all courses (required)
        - isFree: Boolean flag indicating if course is free (defaults to false)
        - subjectNames: Array of existing subject names to associate with the course (optional)
      parameters:
      - description: course details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.CourseForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateCourse
      tags:
      - courses
  /courses/{course_id}/tests/{test_id}:
    post:
      consumes:
      - application/json
      description: associate an existing test with an existing course
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: integer
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Associate Test with Course
      tags:
      - courses
  /courses/{id}:
    get:
      consumes:
      - application/json
      description: get detailed information about a specific course including subjects,
        chapters, videos, and study materials
      parameters:
      - description: Course ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CourseDetails'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Course Details
      tags:
      - courses
  /enroll/{course_id}:
    post:
      consumes:
      - application/json
      description: enroll student in a course
      parameters:
      - description: course ID to enroll in
        in: path
        name: course_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Student'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: EnrollInCourse
      tags:
      - students
  /formula-cards:
    get:
      consumes:
      - application/json
      description: get all formula cards organized by subject, chapter, and topic
        with counts
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.FormulaCardsBySubject'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Formula Cards
      tags:
      - formula-cards
    post:
      consumes:
      - application/json
      description: |-
        create multiple formula cards for a topic

        Field Constraints:
        - subjectName: Must reference an existing subject (required)
        - chapterName: Must reference an existing chapter within the subject (required)
        - topicName: Must reference an existing topic within the chapter (required)
        - formulaCards: Array must contain at least 1 formula card (required)
        - name: Required for each formula card
        - imageUrl: Required for each formula card
      parameters:
      - description: formula cards details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.FormulaCardsForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SimpleEntityResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateFormulaCards
      tags:
      - formula-cards
  /institutions:
    get:
      consumes:
      - application/json
      description: get all institutions
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Institution'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Institutions
      tags:
      - institutions
    post:
      consumes:
      - application/json
      description: |-
        create a new institution

        Field Constraints:
        - name: Institution name (required)
        - city_or_town: City or town where institution is located (required)
        - state: State where institution is located (required)
        - contact_name: Name of the contact person (required)
        - contact_number: Contact phone number (required)
      parameters:
      - description: institution details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.InstitutionForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Create Institution
      tags:
      - institutions
  /institutions/{id}:
    delete:
      consumes:
      - application/json
      description: delete an institution (soft delete)
      parameters:
      - description: Institution ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Delete Institution
      tags:
      - institutions
    put:
      consumes:
      - application/json
      description: |-
        update an existing institution

        Field Constraints:
        - name: Institution name (required)
        - city_or_town: City or town where institution is located (required)
        - state: State where institution is located (required)
        - contact_name: Name of the contact person (required)
        - contact_number: Contact phone number (required)
      parameters:
      - description: Institution ID
        in: path
        name: id
        required: true
        type: integer
      - description: institution details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.InstitutionForUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Institution'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Update Institution
      tags:
      - institutions
  /login:
    post:
      consumes:
      - application/json
      description: login with email and password
      parameters:
      - description: user_email and password
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.Credentials'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: Login
      tags:
      - login
  /previous-year-papers:
    get:
      consumes:
      - application/json
      description: get all previous year papers organized by exam type, sorted by
        year (descending) then month (descending)
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.PreviousYearPapersByExamType'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get All Previous Year Papers Organized by Exam Type
      tags:
      - previous-year-papers
    post:
      consumes:
      - application/json
      description: |-
        create multiple previous year papers in bulk

        Field Constraints:
        - examType: Must be one of 'IIT-JEE', 'NEET' (required)
        - month: Must be between 1 and 12 (required)
        - year: Must be between 1900 and 2100 (required)
        - pdfUrl: Required for each paper
      parameters:
      - description: previous year papers details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.PreviousYearPapersForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SimpleEntityResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreatePreviousYearPapers
      tags:
      - previous-year-papers
  /questions:
    get:
      consumes:
      - application/json
      description: get questions with optional filters for subject, chapter, topic,
        and difficulty
      parameters:
      - description: Subject name (optional)
        in: query
        name: subject
        type: string
      - description: Chapter name (optional)
        in: query
        name: chapter
        type: string
      - description: Topic name (optional)
        in: query
        name: topic
        type: string
      - description: Difficulty level (optional)
        in: query
        name: difficulty
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Question'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Questions
      tags:
      - questions
    post:
      consumes:
      - application/json
      description: |-
        create new question

        Field Constraints:
        - text: Question text is required
        - topicName: Must reference an existing topic (required)
        - difficultyName: Must reference an existing difficulty level (required)
        - questionType: Type of question (e.g., 'MCQ', 'text')
        - correctAnswer: Only used for text questions, not for MCQ questions
        - options: Required for MCQ questions, should contain option details
      parameters:
      - description: question details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.QuestionForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateQuestion
      tags:
      - questions
  /responses:
    post:
      consumes:
      - application/json
      description: Add a response to an existing comment
      parameters:
      - description: response details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.ResponseForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Add Response
      tags:
      - comments
  /section-types:
    get:
      consumes:
      - application/json
      description: get all section types with their associated subjects
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SectionType'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Section Types
      tags:
      - tests
    post:
      consumes:
      - application/json
      description: |-
        create new section type

        Field Constraints:
        - name: Section type name must be unique (required)
        - subjectName: Must reference an existing subject (required)
      parameters:
      - description: section type details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.SectionTypeForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateSectionType
      tags:
      - tests
  /students:
    get:
      consumes:
      - application/json
      description: |-
        get list of all students with optional filters

        Optional Filters:
        - stream: Filter by stream ('IIT-JEE' or 'NEET')
        - class: Filter by class ('9th', '10th', '11th', '12th', 'dropper')
        - name: Prefix match on student's full name
        - email: Prefix match on student's email
        - institution: Exact match on student's institution
        - phone_number: Prefix match on student's phone number
      parameters:
      - description: Stream filter (IIT-JEE or NEET)
        in: query
        name: stream
        type: string
      - description: Class filter (9th, 10th, 11th, 12th, dropper)
        in: query
        name: class
        type: string
      - description: Name prefix filter
        in: query
        name: name
        type: string
      - description: Email prefix filter
        in: query
        name: email
        type: string
      - description: Phone number prefix filter
        in: query
        name: phone_number
        type: string
      - description: Institution exact match filter
        in: query
        name: institution
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.StudentForList'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Students
      tags:
      - students
    post:
      consumes:
      - application/json
      description: |-
        create new student with enhanced information including institute, class, stream, city and state

        Field Constraints:
        - class: Must be one of '9th', '10th', '11th', '12th', 'dropper' (optional)
        - stream: Must be one of 'IIT-JEE', 'NEET' (optional)
        - email: Must be unique across all users
        - phoneNumber: Must be unique across all users
      parameters:
      - description: student details with enhanced fields
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.StudentForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CreatedStudentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: CreateStudent
      tags:
      - students
  /students/send-verification-code:
    post:
      consumes:
      - application/json
      description: |-
        send verification code to student via SMS or email

        Field Constraints:
        - phone_number: Must be a valid phone number
      parameters:
      - description: verification code request details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.SendVerificationCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SendVerificationCodeResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: SendVerificationCode
      tags:
      - students
  /students/verify-code:
    post:
      consumes:
      - application/json
      description: |-
        verify the code entered by student

        Field Constraints:
        - phone_number: Must be a valid phone number
        - code: Must be the verification code received via SMS
      parameters:
      - description: verification code details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.VerifyCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.VerifyCodeResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      summary: VerifyCode
      tags:
      - students
  /studymaterials:
    post:
      consumes:
      - application/json
      description: add a new material
      parameters:
      - description: material details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.MaterialForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddStudyMaterial
      tags:
      - library
  /subjects:
    get:
      consumes:
      - application/json
      description: get subjects for logged in student
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Subject'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Subjects
      tags:
      - library
    post:
      consumes:
      - application/json
      description: |-
        create new subject

        Field Constraints:
        - name: Subject name must be unique (required)
        - displayName: Display name for the subject (required)
      parameters:
      - description: subject details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.SubjectForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateSubject
      tags:
      - subjects
  /test-responses:
    post:
      consumes:
      - application/json
      description: Record student responses for all questions in a test
      parameters:
      - description: test responses
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestResponsesForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestResponsesResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: RecordTestResponses
      tags:
      - test-responses
  /test-responses/{test_id}:
    get:
      consumes:
      - application/json
      description: Get student responses for a specific test with total score and
        recording timestamp
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StudentTestResponsesResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: GetStudentTestResponses
      tags:
      - test-responses
  /test-responses/evaluate:
    post:
      consumes:
      - application/json
      description: Evaluate all unevaluated responses for a specific test (Admin only)
      parameters:
      - description: test evaluation request
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestEvaluationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestEvaluationResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: EvaluateTestResponses
      tags:
      - test-responses
  /test-responses/rankings/{test_id}:
    get:
      consumes:
      - application/json
      description: Get rankings for all students in a specific test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      - description: 'Limit number of results (default: 100)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestRankingResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: GetTestRankings
      tags:
      - test-responses
  /test-types:
    get:
      consumes:
      - application/json
      description: get all test types with their associated section types
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TestType'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Test Types
      tags:
      - tests
    post:
      consumes:
      - application/json
      description: |-
        create new test type

        Field Constraints:
        - name: Test type name must be unique (required)
      parameters:
      - description: test type details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestTypeForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTestType
      tags:
      - tests
  /tests:
    get:
      consumes:
      - application/json
      description: get tests for the logged in user. Students see tests from enrolled
        courses and all ZSAT type tests. Admins see all tests.
      parameters:
      - description: Filter by active status (true/false)
        in: query
        name: active
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TestForGet'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Tests
      tags:
      - tests
    post:
      consumes:
      - application/json
      description: |-
        create new test of a given type

        Field Constraints:
        - name: Test name is required
        - testTypeName: Must reference an existing test type (required)
        - fromTime: Start time for the test (required)
        - toTime: End time for the test (required)
        - sections: Array of sections for the test
      parameters:
      - description: test details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TestForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTest
      tags:
      - tests
  /tests/{test_id}/active:
    put:
      consumes:
      - application/json
      description: toggle active status of a test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Toggle Test Active Status
      tags:
      - tests
  /tests/{test_id}/questions:
    delete:
      consumes:
      - application/json
      description: remove questions from a test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      - description: question IDs and section name
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.RemoveQuestionsFromTestRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: RemoveQuestionsFromTest
      tags:
      - tests
    get:
      consumes:
      - application/json
      description: get all questions for a specific test organized by sections
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TestQuestionsResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Test Questions
      tags:
      - tests
    post:
      consumes:
      - application/json
      description: add questions to a test
      parameters:
      - description: Test ID
        in: path
        name: test_id
        required: true
        type: integer
      - description: question IDs and section name
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.AddQuestionsToTestRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddQuestionsToTest
      tags:
      - tests
  /topics:
    get:
      consumes:
      - application/json
      description: get topics for a chapter_name, or all topics if no chapter_name
        provided
      parameters:
      - description: Chapter Name (optional)
        in: query
        name: chapter_name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Topic'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Topics
      tags:
      - questions
    post:
      consumes:
      - application/json
      description: create new topic for questions
      parameters:
      - description: topic details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TopicForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: CreateTopic
      tags:
      - questions
  /transactions:
    get:
      consumes:
      - application/json
      description: Get transaction history for the logged-in student
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TransactionHistory'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Transactions
      tags:
      - transactions
    post:
      consumes:
      - application/json
      description: |-
        Create a new transaction for course purchase

        Field Constraints:
        - courseIds: Array of course IDs to purchase (required, must contain at least 1 ID)
        - paymentMethod: Payment method identifier (required)
        Transaction status will be set to 'PENDING' initially
      parameters:
      - description: transaction details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TransactionForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Transaction'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Create Transaction
      tags:
      - transactions
  /transactions/{id}:
    get:
      consumes:
      - application/json
      description: Get specific transaction details by ID
      parameters:
      - description: transaction ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Transaction'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Get Transaction by ID
      tags:
      - transactions
  /transactions/{id}/status:
    put:
      consumes:
      - application/json
      description: |-
        Update transaction status (admin/webhook endpoint)

        Field Constraints:
        - status: Must be one of 'PENDING', 'COMPLETED', 'FAILED', 'CANCELLED' (required)
        - paymentReference: External payment gateway reference (optional)
      parameters:
      - description: transaction ID
        in: path
        name: id
        required: true
        type: integer
      - description: status update details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.TransactionStatusUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: Update Transaction Status
      tags:
      - transactions
  /users/password:
    post:
      consumes:
      - application/json
      description: update password for logged in user
      parameters:
      - description: new password
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.UpdatePassword'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: UpdatePassword
      tags:
      - users
  /videos:
    post:
      consumes:
      - application/json
      description: add a new video
      parameters:
      - description: video details
        in: body
        name: item
        required: true
        schema:
          $ref: '#/definitions/models.VideoForCreate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SimpleEntityResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/http.HTTPError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/http.HTTPError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/http.HTTPError'
      security:
      - BearerAuth: []
      summary: AddVideo
      tags:
      - library
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
